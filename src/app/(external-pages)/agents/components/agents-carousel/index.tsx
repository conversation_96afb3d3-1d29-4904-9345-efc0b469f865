"use client";

import React, { useRef, useState, useEffect } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";

const HorizontalCarousel = ({
  setAgentsCategory,
  categories = ["All"],
}: {
  setAgentsCategory: React.Dispatch<React.SetStateAction<string>>;
  categories?: string[];
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const pathname = usePathname();

  // Get the active category from the URL
  const activeCategory = pathname.split("/").pop() || "";
  const activeCategoryIndex = categories.findIndex(
    (category) => category.toLowerCase().replace(/\s+/g, "-") === activeCategory
  );
  const [selectedItem, setSelectedItem] = useState(
    activeCategoryIndex !== -1 ? activeCategoryIndex : 0
  );

  const updateScrollButtons = () => {
    const container = containerRef.current;
    if (container) {
      setCanScrollLeft(container.scrollLeft > 0);
      setCanScrollRight(
        container.scrollLeft + container.clientWidth < container.scrollWidth
      );
    }
  };

  const scroll = (direction: "left" | "right") => {
    const container = containerRef.current;
    if (container) {
      const scrollAmount = container.clientWidth / 2;
      container.scrollBy({
        left: direction === "left" ? -scrollAmount : scrollAmount,
        behavior: "smooth",
      });
    }
  };

  useEffect(() => {
    const container = containerRef.current;
    updateScrollButtons();
    if (container) {
      container.addEventListener("scroll", updateScrollButtons);
    }
    return () => {
      container?.removeEventListener("scroll", updateScrollButtons);
    };
  }, []);

  // Update selected item when pathname changes
  useEffect(() => {
    const newActiveIndex = categories.findIndex(
      (category) =>
        category.toLowerCase().replace(/\s+/g, "-") === activeCategory
    );
    if (newActiveIndex !== -1) {
      setSelectedItem(newActiveIndex);
    }
  }, [pathname, categories, activeCategory]);

  return (
    <div className="flex items-center justify-center">
      <div className="relative w-full px-3 md:px-10 xl:max-w-[1550px]">
        {canScrollLeft && (
          <button
            onClick={() => scroll("left")}
            className="absolute left-0 md:left-5 top-1/2 -translate-y-1/2 z-10 p-2 bg-white shadow-md rounded-full"
          >
            <ChevronLeft className="w-5 h-5" />
          </button>
        )}
        <div
          ref={containerRef}
          className="flex overflow-x-auto no-scrollbar space-x-2 py-2"
        >
          {categories.map((item, index) => (
            <Link
              key={index}
              href={
                item.toLowerCase() === "all"
                  ? "/agents"
                  : `/agents/category/${item.toLowerCase().replace(/\s+/g, "-")}`
              }
              className={`whitespace-nowrap px-2 md:px-4 py-1 md:py-2 rounded-full text-xs md:text-sm border ${
                index === selectedItem
                  ? "bg-[#8686F9] border-violet-300 text-white"
                  : "bg-white border-gray-200 text-gray-600 hover:bg-gray-50"
              }`}
            >
              {item}
            </Link>
          ))}
        </div>
        {canScrollRight && (
          <button
            onClick={() => scroll("right")}
            className="absolute right-0 md:right-5 top-1/2 -translate-y-1/2 z-10 p-2 bg-white shadow-md rounded-full"
          >
            <ChevronRight className="w-5 h-5" />
          </button>
        )}
      </div>
    </div>
  );
};

export default HorizontalCarousel;
